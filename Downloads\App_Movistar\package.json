{"name": "MovistarAuditApp", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-image-picker": "^7.0.3", "exceljs": "^4.4.0", "react-native-fs": "^2.20.0", "@react-native-async-storage/async-storage": "^1.19.3", "react-native-date-picker": "^4.3.3", "react-native-vector-icons": "^10.0.0", "react-native-permissions": "^3.10.1", "@react-native-picker/picker": "^2.5.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}