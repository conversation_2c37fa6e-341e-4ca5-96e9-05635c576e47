import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
  AUDIT_DATA: 'audit_data',
  PHOTOS: 'audit_photos',
};

export const StorageService = {
  // Guardar datos de auditoría
  async saveAuditData(data) {
    try {
      const jsonValue = JSON.stringify(data);
      await AsyncStorage.setItem(STORAGE_KEYS.AUDIT_DATA, jsonValue);
      return true;
    } catch (error) {
      console.error('Error saving audit data:', error);
      return false;
    }
  },

  // Obtener datos de auditoría
  async getAuditData() {
    try {
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.AUDIT_DATA);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error('Error getting audit data:', error);
      return null;
    }
  },

  // Guardar fotos
  async savePhotos(photos) {
    try {
      const jsonValue = JSON.stringify(photos);
      await AsyncStorage.setItem(STORAGE_KEYS.PHOTOS, jsonValue);
      return true;
    } catch (error) {
      console.error('Error saving photos:', error);
      return false;
    }
  },

  // Obtener fotos
  async getPhotos() {
    try {
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.PHOTOS);
      return jsonValue != null ? JSON.parse(jsonValue) : {};
    } catch (error) {
      console.error('Error getting photos:', error);
      return {};
    }
  },

  // Limpiar todos los datos
  async clearAllData() {
    try {
      await AsyncStorage.multiRemove([STORAGE_KEYS.AUDIT_DATA, STORAGE_KEYS.PHOTOS]);
      return true;
    } catch (error) {
      console.error('Error clearing data:', error);
      return false;
    }
  },
};
