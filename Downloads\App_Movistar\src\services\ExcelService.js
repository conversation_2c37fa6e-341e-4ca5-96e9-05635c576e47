import ExcelJS from 'exceljs';
import RNFS from 'react-native-fs';
import { Alert, PermissionsAndroid, Platform } from 'react-native';

export class ExcelService {
  static async generateAuditReport(auditData, photos) {
    try {
      // Solicitar permisos en Android
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Permiso de almacenamiento',
            message: 'La aplicación necesita acceso al almacenamiento para guardar el archivo Excel.',
            buttonNeutral: 'Preguntar después',
            buttonNegative: 'Cancelar',
            buttonPositive: 'OK',
          }
        );
        
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Alert.alert('Error', 'Se necesitan permisos de almacenamiento para guardar el archivo.');
          return null;
        }
      }

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Auditoría Movistar');

      // Configurar el ancho de las columnas
      worksheet.columns = [
        { width: 30 }, // A - Descripción
        { width: 25 }, // B - Foto Hallazgo
        { width: 25 }, // C - Foto Corregida
      ];

      // Título principal
      worksheet.mergeCells('A1:C1');
      const titleCell = worksheet.getCell('A1');
      titleCell.value = 'AUDITORÍA TÉCNICA - INSTALACIONES FIBRA ÓPTICA MOVISTAR';
      titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
      titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF0066CC' } };
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getRow(1).height = 30;

      // Información de la auditoría
      let currentRow = 3;
      const infoData = [
        ['Tipo de Solicitud:', auditData.tipoSolicitud || ''],
        ['Número de Orden:', auditData.numeroOrden || ''],
        ['Nombre del Auditor:', auditData.nombreAuditor || ''],
        ['Fecha de Auditoría:', auditData.fechaAuditoria ? new Date(auditData.fechaAuditoria).toLocaleDateString('es-ES') : ''],
        ['Empresa Contratista:', auditData.empresaContratista || ''],
        ['Nombre del Técnico:', auditData.nombreTecnico || ''],
        ['Cédula del Técnico:', auditData.cedulaTecnico || ''],
      ];

      infoData.forEach(([label, value]) => {
        worksheet.getCell(`A${currentRow}`).value = label;
        worksheet.getCell(`A${currentRow}`).font = { bold: true };
        worksheet.getCell(`B${currentRow}`).value = value;
        worksheet.mergeCells(`B${currentRow}:C${currentRow}`);
        currentRow++;
      });

      // Espacio
      currentRow += 2;

      // Encabezados de la tabla de fotos
      worksheet.getCell(`A${currentRow}`).value = 'ÍTEM DE VERIFICACIÓN';
      worksheet.getCell(`B${currentRow}`).value = 'FOTO CON HALLAZGO';
      worksheet.getCell(`C${currentRow}`).value = 'FOTO CORREGIDA';

      // Estilo de encabezados
      ['A', 'B', 'C'].forEach(col => {
        const cell = worksheet.getCell(`${col}${currentRow}`);
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF0066CC' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      worksheet.getRow(currentRow).height = 25;
      currentRow++;

      // Datos de fotos
      const photoItems = [
        { id: 'instalacion_interna', titulo: 'Validación de instalación interna – Calidad/Estética' },
        { id: 'instalacion_externa', titulo: 'Validación de instalación externa – Calidad/Estética' },
        { id: 'marquilla', titulo: 'Validación marquilla (Roseta abierta y cerrada)' },
        { id: 'ubicacion_hgu', titulo: 'Ubicación de HGU (funcionalidad / estética)' },
        { id: 'bassport', titulo: 'Validación de Bassport (ubicación, instalación, configuración)' },
        { id: 'test_velocidad', titulo: 'Test de velocidad (captura de pantalla del SpeedTest)' },
        { id: 'cto', titulo: 'CTO' },
        { id: 'sistema_tecnico', titulo: 'Captura del sistema técnico (Opcional)' },
      ];

      for (const item of photoItems) {
        const itemPhotos = photos[item.id];
        
        // Descripción del ítem
        worksheet.getCell(`A${currentRow}`).value = item.titulo;
        worksheet.getCell(`A${currentRow}`).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getCell(`A${currentRow}`).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // Agregar imágenes si existen
        if (itemPhotos) {
          if (itemPhotos.hallazgo) {
            await this.addImageToCell(workbook, worksheet, itemPhotos.hallazgo.uri, `B${currentRow}`);
          }
          if (itemPhotos.corregida) {
            await this.addImageToCell(workbook, worksheet, itemPhotos.corregida.uri, `C${currentRow}`);
          }
        }

        // Estilo de celdas de fotos
        ['B', 'C'].forEach(col => {
          const cell = worksheet.getCell(`${col}${currentRow}`);
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });

        worksheet.getRow(currentRow).height = 100; // Altura para las imágenes
        currentRow++;
      }

      // Generar el archivo
      const fileName = `Auditoria_${auditData.numeroOrden || 'SinNumero'}_${new Date().toISOString().split('T')[0]}.xlsx`;
      const filePath = `${RNFS.DownloadDirectoryPath}/${fileName}`;

      const buffer = await workbook.xlsx.writeBuffer();
      const base64 = buffer.toString('base64');
      
      await RNFS.writeFile(filePath, base64, 'base64');

      Alert.alert(
        'Éxito',
        `Archivo guardado exitosamente en:\n${filePath}`,
        [{ text: 'OK' }]
      );

      return filePath;
    } catch (error) {
      console.error('Error generating Excel:', error);
      Alert.alert('Error', 'No se pudo generar el archivo Excel: ' + error.message);
      return null;
    }
  }

  static async addImageToCell(workbook, worksheet, imageUri, cellAddress) {
    try {
      // Leer la imagen como base64
      const imageBase64 = await RNFS.readFile(imageUri, 'base64');
      
      // Agregar imagen al workbook
      const imageId = workbook.addImage({
        base64: imageBase64,
        extension: 'jpeg',
      });

      // Insertar imagen en la celda
      worksheet.addImage(imageId, {
        tl: { col: cellAddress.charCodeAt(0) - 65, row: parseInt(cellAddress.slice(1)) - 1 },
        ext: { width: 150, height: 100 },
      });
    } catch (error) {
      console.error('Error adding image to cell:', error);
    }
  }
}
