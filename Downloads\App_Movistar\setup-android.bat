@echo off
echo ========================================
echo   Configuracion Android Development
echo ========================================
echo.

echo PASO 1: Configurando variables de entorno...
echo.

REM Rutas típicas de Android SDK
set ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk
set ANDROID_SDK_ROOT=%LOCALAPPDATA%\Android\Sdk

echo Verificando si Android SDK existe en: %ANDROID_HOME%
if exist "%ANDROID_HOME%" (
    echo ✓ Android SDK encontrado
) else (
    echo ✗ Android SDK no encontrado en la ruta por defecto
    echo.
    echo Por favor, instala Android Studio primero:
    echo https://developer.android.com/studio
    echo.
    pause
    exit /b 1
)

echo.
echo PASO 2: Agregando rutas al PATH...

REM Agregar rutas al PATH del sistema
setx ANDROID_HOME "%ANDROID_HOME%"
setx ANDROID_SDK_ROOT "%ANDROID_SDK_ROOT%"

REM Agregar herramientas de Android al PATH
setx PATH "%PATH%;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools;%ANDROID_HOME%\tools\bin;%ANDROID_HOME%\emulator"

echo.
echo PASO 3: Verificando instalacion...
echo.

REM Verificar ADB
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo ✓ ADB encontrado
) else (
    echo ✗ ADB no encontrado. Instala Android SDK Platform Tools desde Android Studio
)

REM Verificar Emulator
if exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo ✓ Emulator encontrado
) else (
    echo ✗ Emulator no encontrado. Instala Android Emulator desde Android Studio
)

echo.
echo PASO 4: Instrucciones adicionales...
echo.
echo 1. Abre Android Studio
echo 2. Ve a Tools ^> SDK Manager
echo 3. Instala:
echo    - Android SDK Platform-Tools
echo    - Android SDK Build-Tools
echo    - Android Emulator
echo    - Al menos una version de Android API (recomendado: API 33)
echo.
echo 4. Ve a Tools ^> AVD Manager
echo 5. Crea un dispositivo virtual (AVD)
echo.
echo 6. REINICIA tu terminal/PowerShell para aplicar las variables de entorno
echo.
echo ========================================
echo   Configuracion completada
echo ========================================
pause
