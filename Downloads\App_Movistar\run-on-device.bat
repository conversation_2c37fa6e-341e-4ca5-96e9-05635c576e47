@echo off
echo ========================================
echo   Ejecutar en Dispositivo Fisico
echo ========================================
echo.

echo INSTRUCCIONES PARA DISPOSITIVO FISICO:
echo.
echo 1. En tu telefono Android:
echo    - Ve a Configuracion ^> Acerca del telefono
echo    - Toca 7 veces en "Numero de compilacion"
echo    - Activa "Opciones de desarrollador"
echo    - Activa "Depuracion USB"
echo.
echo 2. Conecta tu telefono por USB
echo.
echo 3. Acepta la depuracion USB en el telefono
echo.

echo Verificando dispositivos conectados...
adb devices
echo.

echo Si aparece tu dispositivo, presiona cualquier tecla para continuar...
pause

echo.
echo Instalando la aplicacion en el dispositivo...
echo.

REM Ir al directorio del proyecto
cd /d "%~dp0"

REM Instalar dependencias si no existen
if not exist "node_modules" (
    echo Instalando dependencias...
    npm install
)

REM Ejecutar en dispositivo
echo Ejecutando aplicacion...
npx react-native run-android --device

echo.
echo ========================================
echo   Proceso completado
echo ========================================
pause
