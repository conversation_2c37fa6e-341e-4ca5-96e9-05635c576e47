import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import FormularioDatos from './src/components/FormularioDatos';
import RegistroFotografico from './src/components/RegistroFotografico';
import { ExcelService } from './src/services/ExcelService';
import { StorageService } from './src/utils/storage';
import { globalStyles, colors } from './src/styles/styles';

const App = () => {
  const [currentStep, setCurrentStep] = useState(0); // 0: Formulario, 1: Fotos
  const [auditData, setAuditData] = useState({});
  const [photos, setPhotos] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const steps = ['Datos de Auditoría', 'Registro Fotográfico'];

  useEffect(() => {
    loadSavedData();
  }, []);

  const loadSavedData = async () => {
    try {
      const savedAuditData = await StorageService.getAuditData();
      const savedPhotos = await StorageService.getPhotos();
      
      if (savedAuditData) {
        setAuditData(savedAuditData);
      }
      
      if (savedPhotos) {
        setPhotos(savedPhotos);
      }
    } catch (error) {
      console.error('Error loading saved data:', error);
    }
  };

  const handleDataChange = (newData) => {
    setAuditData(newData);
  };

  const handlePhotosChange = (newPhotos) => {
    setPhotos(newPhotos);
  };

  const validateCurrentStep = () => {
    if (currentStep === 0) {
      // Validar formulario
      const requiredFields = [
        'tipoSolicitud',
        'numeroOrden',
        'nombreAuditor',
        'empresaContratista',
        'nombreTecnico',
        'cedulaTecnico',
      ];

      for (let field of requiredFields) {
        if (!auditData[field] || auditData[field].trim() === '') {
          Alert.alert('Error', `El campo ${getFieldLabel(field)} es obligatorio`);
          return false;
        }
      }
      return true;
    }
    
    return true; // Para el paso de fotos, la validación es opcional
  };

  const getFieldLabel = (field) => {
    const labels = {
      tipoSolicitud: 'Tipo de Solicitud',
      numeroOrden: 'Número de Orden',
      nombreAuditor: 'Nombre del Auditor',
      empresaContratista: 'Empresa Contratista',
      nombreTecnico: 'Nombre del Técnico',
      cedulaTecnico: 'Cédula del Técnico',
    };
    return labels[field] || field;
  };

  const nextStep = () => {
    if (validateCurrentStep()) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const generateExcel = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    setIsLoading(true);
    
    try {
      const filePath = await ExcelService.generateAuditReport(auditData, photos);
      if (filePath) {
        Alert.alert(
          'Éxito',
          'El archivo Excel se ha generado correctamente.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Opcional: Preguntar si quiere crear nueva auditoría
                Alert.alert(
                  'Nueva Auditoría',
                  '¿Desea crear una nueva auditoría?',
                  [
                    { text: 'No', style: 'cancel' },
                    { text: 'Sí', onPress: createNewAudit },
                  ]
                );
              },
            },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo generar el archivo Excel.');
    } finally {
      setIsLoading(false);
    }
  };

  const createNewAudit = async () => {
    Alert.alert(
      'Confirmar',
      '¿Está seguro de que quiere crear una nueva auditoría? Se perderán todos los datos actuales.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Confirmar',
          style: 'destructive',
          onPress: async () => {
            await StorageService.clearAllData();
            setAuditData({});
            setPhotos({});
            setCurrentStep(0);
            Alert.alert('Éxito', 'Nueva auditoría creada.');
          },
        },
      ]
    );
  };

  const renderStepIndicator = () => (
    <View style={{ flexDirection: 'row', justifyContent: 'center', padding: 20 }}>
      {steps.map((step, index) => (
        <View key={index} style={{ alignItems: 'center', flex: 1 }}>
          <View
            style={{
              width: 30,
              height: 30,
              borderRadius: 15,
              backgroundColor: index <= currentStep ? colors.primary : colors.lightGray,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 5,
            }}
          >
            <Text
              style={{
                color: index <= currentStep ? colors.white : colors.darkGray,
                fontWeight: 'bold',
              }}
            >
              {index + 1}
            </Text>
          </View>
          <Text
            style={{
              fontSize: 12,
              color: index <= currentStep ? colors.primary : colors.darkGray,
              textAlign: 'center',
            }}
          >
            {step}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <FormularioDatos
            onDataChange={handleDataChange}
            initialData={auditData}
          />
        );
      case 1:
        return (
          <RegistroFotografico
            onPhotosChange={handlePhotosChange}
            initialPhotos={photos}
          />
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={globalStyles.container}>
      <StatusBar backgroundColor={colors.primary} barStyle="light-content" />
      
      {/* Header */}
      <View style={globalStyles.header}>
        <Text style={globalStyles.headerTitle}>Auditoría Movistar</Text>
      </View>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Content */}
      <View style={{ flex: 1 }}>
        {renderCurrentStep()}
      </View>

      {/* Navigation Buttons */}
      <View style={{ padding: 20 }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          {currentStep > 0 && (
            <TouchableOpacity
              style={[globalStyles.secondaryButton, { flex: 1, marginRight: 10 }]}
              onPress={prevStep}
            >
              <Text style={globalStyles.buttonText}>Anterior</Text>
            </TouchableOpacity>
          )}

          {currentStep < steps.length - 1 ? (
            <TouchableOpacity
              style={[globalStyles.button, { flex: 1 }]}
              onPress={nextStep}
            >
              <Text style={globalStyles.buttonText}>Siguiente</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[globalStyles.button, { flex: 1 }]}
              onPress={generateExcel}
              disabled={isLoading}
            >
              <Text style={globalStyles.buttonText}>
                {isLoading ? 'Generando...' : 'Generar Excel'}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* New Audit Button */}
        <TouchableOpacity
          style={[globalStyles.secondaryButton, { marginTop: 10 }]}
          onPress={createNewAudit}
        >
          <Text style={globalStyles.buttonText}>Nueva Auditoría</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default App;
