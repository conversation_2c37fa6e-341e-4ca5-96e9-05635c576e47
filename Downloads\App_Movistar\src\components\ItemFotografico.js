import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import { launchImageLibrary, launchCamera } from 'react-native-image-picker';
import { globalStyles, colors } from '../styles/styles';

const ItemFotografico = ({ 
  titulo, 
  onPhotosChange, 
  photos = { hallazgo: null, corregida: null },
  showCorregida = true 
}) => {
  const [currentPhotos, setCurrentPhotos] = useState(photos);

  const showImagePicker = (tipo) => {
    Alert.alert(
      'Seleccionar imagen',
      'Elige una opción',
      [
        { text: 'Cámara', onPress: () => openCamera(tipo) },
        { text: 'Galería', onPress: () => openGallery(tipo) },
        { text: 'Cancelar', style: 'cancel' },
      ]
    );
  };

  const imagePickerOptions = {
    mediaType: 'photo',
    quality: 0.8,
    maxWidth: 1024,
    maxHeight: 1024,
    includeBase64: false,
  };

  const openCamera = (tipo) => {
    launchCamera(imagePickerOptions, (response) => {
      handleImageResponse(response, tipo);
    });
  };

  const openGallery = (tipo) => {
    launchImageLibrary(imagePickerOptions, (response) => {
      handleImageResponse(response, tipo);
    });
  };

  const handleImageResponse = (response, tipo) => {
    if (response.didCancel || response.error) {
      return;
    }

    if (response.assets && response.assets[0]) {
      const imageUri = response.assets[0].uri;
      const newPhotos = {
        ...currentPhotos,
        [tipo]: {
          uri: imageUri,
          fileName: response.assets[0].fileName || `${tipo}_${Date.now()}.jpg`,
          type: response.assets[0].type || 'image/jpeg',
        },
      };
      
      setCurrentPhotos(newPhotos);
      onPhotosChange(newPhotos);
    }
  };

  const removePhoto = (tipo) => {
    Alert.alert(
      'Eliminar foto',
      '¿Estás seguro de que quieres eliminar esta foto?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: () => {
            const newPhotos = {
              ...currentPhotos,
              [tipo]: null,
            };
            setCurrentPhotos(newPhotos);
            onPhotosChange(newPhotos);
          },
        },
      ]
    );
  };

  const renderPhotoSection = (tipo, label) => (
    <View style={{ flex: 1, marginHorizontal: 5 }}>
      <Text style={[globalStyles.label, { fontSize: 14, textAlign: 'center' }]}>
        {label}
      </Text>
      
      {currentPhotos[tipo] ? (
        <View>
          <Image
            source={{ uri: currentPhotos[tipo].uri }}
            style={[globalStyles.photoPreview, { width: '100%', height: 120 }]}
            resizeMode="cover"
          />
          <TouchableOpacity
            style={[globalStyles.photoButton, { backgroundColor: colors.error, marginTop: 5 }]}
            onPress={() => removePhoto(tipo)}
          >
            <Text style={[globalStyles.photoButtonText, { color: colors.white }]}>
              Eliminar
            </Text>
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity
          style={[globalStyles.photoButton, { height: 120, justifyContent: 'center' }]}
          onPress={() => showImagePicker(tipo)}
        >
          <Text style={globalStyles.photoButtonText}>📷</Text>
          <Text style={[globalStyles.photoButtonText, { fontSize: 12 }]}>
            Tomar/Subir foto
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={[globalStyles.card, { marginVertical: 10 }]}>
      <Text style={[globalStyles.label, { fontSize: 16, marginBottom: 15, textAlign: 'center' }]}>
        {titulo}
      </Text>
      
      <View style={{ flexDirection: 'row' }}>
        {renderPhotoSection('hallazgo', 'Foto con Hallazgo')}
        {showCorregida && renderPhotoSection('corregida', 'Foto Corregida')}
      </View>
    </View>
  );
};

export default ItemFotografico;
