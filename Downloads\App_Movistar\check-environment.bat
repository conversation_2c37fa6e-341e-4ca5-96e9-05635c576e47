@echo off
echo ========================================
echo   Diagnostico del Entorno de Desarrollo
echo ========================================
echo.

echo Verificando Node.js...
node --version
if %errorlevel% neq 0 (
    echo ✗ Node.js no encontrado. Instala desde: https://nodejs.org/
) else (
    echo ✓ Node.js instalado
)

echo.
echo Verificando npm...
npm --version
if %errorlevel% neq 0 (
    echo ✗ npm no encontrado
) else (
    echo ✓ npm instalado
)

echo.
echo Verificando React Native CLI...
npx react-native --version
if %errorlevel% neq 0 (
    echo ✗ React Native CLI no encontrado
    echo Instalando...
    npm install -g @react-native-community/cli
) else (
    echo ✓ React Native CLI instalado
)

echo.
echo Verificando Java...
java -version
if %errorlevel% neq 0 (
    echo ✗ Java no encontrado. Instala JDK 11 o superior
) else (
    echo ✓ Java instalado
)

echo.
echo Verificando variables de entorno Android...
if defined ANDROID_HOME (
    echo ✓ ANDROID_HOME: %ANDROID_HOME%
) else (
    echo ✗ ANDROID_HOME no configurado
)

if defined ANDROID_SDK_ROOT (
    echo ✓ ANDROID_SDK_ROOT: %ANDROID_SDK_ROOT%
) else (
    echo ✗ ANDROID_SDK_ROOT no configurado
)

echo.
echo Verificando herramientas Android...
adb version
if %errorlevel% neq 0 (
    echo ✗ ADB no encontrado en PATH
) else (
    echo ✓ ADB disponible
)

echo.
echo Verificando emuladores...
emulator -list-avds
if %errorlevel% neq 0 (
    echo ✗ Comando emulator no encontrado o sin AVDs
) else (
    echo ✓ Emuladores disponibles
)

echo.
echo Ejecutando React Native Doctor...
npx react-native doctor

echo.
echo ========================================
echo   Diagnostico completado
echo ========================================
pause
