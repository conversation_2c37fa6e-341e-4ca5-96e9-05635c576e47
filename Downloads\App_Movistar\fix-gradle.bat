@echo off
echo ========================================
echo   Reparando Gradle Wrapper
echo ========================================
echo.

cd /d "%~dp0"

echo Creando directorios necesarios...
if not exist "android\gradle\wrapper" mkdir "android\gradle\wrapper"

echo.
echo Descargando Gradle Wrapper JAR...

REM Crear el directorio si no existe
if not exist "android\gradle\wrapper" mkdir "android\gradle\wrapper"

REM Descargar gradle-wrapper.jar usando PowerShell
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v7.5.1/gradle/wrapper/gradle-wrapper.jar' -OutFile 'android\gradle\wrapper\gradle-wrapper.jar'}"

if exist "android\gradle\wrapper\gradle-wrapper.jar" (
    echo ✓ gradle-wrapper.jar descargado exitosamente
) else (
    echo ✗ Error descargando gradle-wrapper.jar
    echo.
    echo Intentando metodo alternativo...
    
    REM Metodo alternativo: usar curl si esta disponible
    curl -L -o "android\gradle\wrapper\gradle-wrapper.jar" "https://github.com/gradle/gradle/raw/v7.5.1/gradle/wrapper/gradle-wrapper.jar"
    
    if exist "android\gradle\wrapper\gradle-wrapper.jar" (
        echo ✓ gradle-wrapper.jar descargado con curl
    ) else (
        echo ✗ No se pudo descargar gradle-wrapper.jar
        echo.
        echo SOLUCION MANUAL:
        echo 1. Ve a: https://github.com/gradle/gradle/raw/v7.5.1/gradle/wrapper/gradle-wrapper.jar
        echo 2. Descarga el archivo manualmente
        echo 3. Guardalo en: android\gradle\wrapper\gradle-wrapper.jar
        echo.
        pause
        exit /b 1
    )
)

echo.
echo Verificando permisos del gradlew...
if exist "android\gradlew.bat" (
    echo ✓ gradlew.bat existe
) else (
    echo ✗ gradlew.bat no encontrado
)

echo.
echo Limpiando cache de Gradle...
cd android
if exist "android\gradlew.bat" (
    call gradlew.bat clean
) else (
    echo Saltando limpieza - gradlew no disponible
)

cd ..

echo.
echo ========================================
echo   Reparacion completada
echo ========================================
echo.
echo Ahora puedes intentar ejecutar:
echo npx react-native run-android
echo.
pause
