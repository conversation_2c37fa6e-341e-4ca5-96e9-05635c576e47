import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Picker } from '@react-native-picker/picker';
import { globalStyles, colors } from '../styles/styles';
import { StorageService } from '../utils/storage';

const FormularioDatos = ({ onDataChange, initialData }) => {
  const [formData, setFormData] = useState({
    tipoSolicitud: '',
    numeroOrden: '',
    nombreAuditor: '',
    fechaAuditoria: new Date(),
    empresaContratista: '',
    nombreTecnico: '',
    cedulaTecnico: '',
  });

  const [showDatePicker, setShowDatePicker] = useState(false);

  useEffect(() => {
    if (initialData) {
      setFormData({
        ...initialData,
        fechaAuditoria: initialData.fechaAuditoria ? new Date(initialData.fechaAuditoria) : new Date(),
      });
    }
  }, [initialData]);

  const handleInputChange = (field, value) => {
    const newData = { ...formData, [field]: value };
    setFormData(newData);
    onDataChange(newData);
    
    // Guardar automáticamente en storage
    StorageService.saveAuditData(newData);
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const validateForm = () => {
    const requiredFields = [
      'tipoSolicitud',
      'numeroOrden',
      'nombreAuditor',
      'empresaContratista',
      'nombreTecnico',
      'cedulaTecnico',
    ];

    for (let field of requiredFields) {
      if (!formData[field] || formData[field].trim() === '') {
        Alert.alert('Error', `El campo ${getFieldLabel(field)} es obligatorio`);
        return false;
      }
    }
    return true;
  };

  const getFieldLabel = (field) => {
    const labels = {
      tipoSolicitud: 'Tipo de Solicitud',
      numeroOrden: 'Número de Orden',
      nombreAuditor: 'Nombre del Auditor',
      empresaContratista: 'Empresa Contratista',
      nombreTecnico: 'Nombre del Técnico',
      cedulaTecnico: 'Cédula del Técnico',
    };
    return labels[field] || field;
  };

  return (
    <ScrollView style={globalStyles.content} showsVerticalScrollIndicator={false}>
      <View style={globalStyles.card}>
        <Text style={globalStyles.sectionTitle}>Datos de la Auditoría</Text>

        {/* Tipo de Solicitud */}
        <Text style={globalStyles.label}>Tipo de Solicitud *</Text>
        <View style={globalStyles.picker}>
          <Picker
            selectedValue={formData.tipoSolicitud}
            onValueChange={(value) => handleInputChange('tipoSolicitud', value)}
            style={{ height: 50 }}
          >
            <Picker.Item label="Seleccionar tipo..." value="" />
            <Picker.Item label="Alta" value="Alta" />
            <Picker.Item label="Avería" value="Avería" />
            <Picker.Item label="Traslado" value="Traslado" />
          </Picker>
        </View>

        {/* Número de Orden */}
        <Text style={globalStyles.label}>Número de Orden *</Text>
        <TextInput
          style={globalStyles.input}
          value={formData.numeroOrden}
          onChangeText={(value) => handleInputChange('numeroOrden', value)}
          placeholder="Ingrese el número de orden"
          keyboardType="numeric"
        />

        {/* Nombre del Auditor */}
        <Text style={globalStyles.label}>Nombre del Auditor *</Text>
        <TextInput
          style={globalStyles.input}
          value={formData.nombreAuditor}
          onChangeText={(value) => handleInputChange('nombreAuditor', value)}
          placeholder="Ingrese el nombre del auditor"
        />

        {/* Fecha de Auditoría */}
        <Text style={globalStyles.label}>Fecha de Auditoría</Text>
        <TouchableOpacity
          style={globalStyles.input}
          onPress={() => setShowDatePicker(true)}
        >
          <Text style={{ fontSize: 16, color: colors.text }}>
            {formatDate(formData.fechaAuditoria)}
          </Text>
        </TouchableOpacity>

        <DatePicker
          modal
          open={showDatePicker}
          date={formData.fechaAuditoria}
          mode="date"
          onConfirm={(date) => {
            setShowDatePicker(false);
            handleInputChange('fechaAuditoria', date);
          }}
          onCancel={() => setShowDatePicker(false)}
          title="Seleccionar fecha"
          confirmText="Confirmar"
          cancelText="Cancelar"
        />

        {/* Empresa Contratista */}
        <Text style={globalStyles.label}>Empresa Contratista *</Text>
        <TextInput
          style={globalStyles.input}
          value={formData.empresaContratista}
          onChangeText={(value) => handleInputChange('empresaContratista', value)}
          placeholder="Ingrese la empresa contratista"
        />

        {/* Nombre del Técnico */}
        <Text style={globalStyles.label}>Nombre del Técnico *</Text>
        <TextInput
          style={globalStyles.input}
          value={formData.nombreTecnico}
          onChangeText={(value) => handleInputChange('nombreTecnico', value)}
          placeholder="Ingrese el nombre del técnico"
        />

        {/* Cédula del Técnico */}
        <Text style={globalStyles.label}>Cédula del Técnico *</Text>
        <TextInput
          style={globalStyles.input}
          value={formData.cedulaTecnico}
          onChangeText={(value) => handleInputChange('cedulaTecnico', value)}
          placeholder="Ingrese la cédula del técnico"
          keyboardType="numeric"
        />
      </View>
    </ScrollView>
  );
};

export default FormularioDatos;
