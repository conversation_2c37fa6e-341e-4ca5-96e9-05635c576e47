# Aplicación de Auditoría Técnica Movistar

Aplicación móvil desarrollada en React Native para realizar auditorías técnicas de instalaciones de fibra óptica para Movistar.

## Características

- ✅ Formulario completo de datos de auditoría
- 📷 Registro fotográfico con cámara o galería
- 📊 Exportación a Excel con formato corporativo
- 💾 Almacenamiento local offline
- 🔄 Funcionalidad de nueva auditoría
- 📱 Diseño responsivo para dispositivos móviles

## Requisitos del Sistema

- Node.js 16 o superior
- React Native CLI
- Android Studio (para desarrollo Android)
- Xcode (para desarrollo iOS - macOS únicamente)

## Instalación

### 1. Clonar e instalar dependencias

```bash
cd Downloads/App_Movistar
npm install
```

### 2. Configuración para Android

```bash
# Instalar dependencias de Android
cd android
./gradlew clean
cd ..

# Ejecutar en Android
npx react-native run-android
```

### 3. Configuración para iOS (solo macOS)

```bash
cd ios
pod install
cd ..

# Ejecutar en iOS
npx react-native run-ios
```

## Dependencias Principales

- **react-native-image-picker**: Captura de fotos desde cámara o galería
- **exceljs**: Generación de archivos Excel
- **react-native-fs**: Manejo de archivos del sistema
- **@react-native-async-storage/async-storage**: Almacenamiento local
- **react-native-date-picker**: Selector de fechas
- **@react-native-picker/picker**: Selector dropdown

## Estructura del Proyecto

```
src/
├── components/
│   ├── FormularioDatos.js      # Formulario de datos básicos
│   ├── RegistroFotografico.js  # Componente principal de fotos
│   └── ItemFotografico.js      # Componente individual de foto
├── services/
│   └── ExcelService.js         # Servicio de generación Excel
├── utils/
│   └── storage.js              # Utilidades de almacenamiento
└── styles/
    └── styles.js               # Estilos globales
```

## Funcionalidades

### 1. Formulario de Datos
- Tipo de solicitud (Alta, Avería, Traslado)
- Número de orden
- Nombre del auditor
- Fecha de auditoría
- Empresa contratista
- Nombre y cédula del técnico

### 2. Registro Fotográfico
- Validación de instalación interna
- Validación de instalación externa
- Validación marquilla (Roseta)
- Ubicación de HGU
- Validación de Bassport
- Test de velocidad
- CTO
- Sistema técnico (opcional)

### 3. Exportación Excel
- Formato corporativo Movistar
- Datos del formulario en encabezado
- Tabla con fotos organizadas por ítem
- Imágenes insertadas en celdas
- Guardado en directorio de descargas

## Permisos Requeridos

### Android
- `CAMERA`: Para tomar fotos
- `WRITE_EXTERNAL_STORAGE`: Para guardar archivos
- `READ_EXTERNAL_STORAGE`: Para leer archivos
- `READ_MEDIA_IMAGES`: Para acceder a galería

### iOS
- `NSCameraUsageDescription`: Acceso a cámara
- `NSPhotoLibraryUsageDescription`: Acceso a galería

## Uso de la Aplicación

1. **Completar Formulario**: Llenar todos los campos obligatorios
2. **Tomar Fotos**: Capturar o seleccionar fotos para cada ítem
3. **Generar Excel**: Exportar el reporte completo
4. **Nueva Auditoría**: Limpiar datos para empezar de nuevo

## Solución de Problemas

### Error de permisos en Android
```bash
# Verificar permisos en AndroidManifest.xml
# Solicitar permisos manualmente en configuración del dispositivo
```

### Error de compilación
```bash
# Limpiar cache
npx react-native start --reset-cache

# Limpiar build de Android
cd android && ./gradlew clean && cd ..

# Reinstalar dependencias
rm -rf node_modules && npm install
```

### Problemas con imágenes
- Verificar permisos de cámara y almacenamiento
- Comprobar espacio disponible en dispositivo
- Reiniciar la aplicación si es necesario

## Desarrollo

### Comandos útiles

```bash
# Iniciar Metro bundler
npx react-native start

# Ejecutar en Android
npx react-native run-android

# Ejecutar en iOS
npx react-native run-ios

# Generar APK de release
cd android && ./gradlew assembleRelease
```

### Estructura de datos

```javascript
// Datos de auditoría
{
  tipoSolicitud: "Alta",
  numeroOrden: "12345",
  nombreAuditor: "Juan Pérez",
  fechaAuditoria: "2024-01-15",
  empresaContratista: "Empresa XYZ",
  nombreTecnico: "Carlos López",
  cedulaTecnico: "12345678"
}

// Estructura de fotos
{
  instalacion_interna: {
    hallazgo: { uri: "file://...", fileName: "...", type: "image/jpeg" },
    corregida: { uri: "file://...", fileName: "...", type: "image/jpeg" }
  },
  // ... otros ítems
}
```

## Contacto y Soporte

Para soporte técnico o reportar problemas, contactar al equipo de desarrollo.

## Licencia

Aplicación desarrollada para uso interno de Movistar.
