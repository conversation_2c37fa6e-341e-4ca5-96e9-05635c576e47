import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Alert,
} from 'react-native';
import ItemFotografico from './ItemFotografico';
import { globalStyles } from '../styles/styles';
import { StorageService } from '../utils/storage';

const RegistroFotografico = ({ onPhotosChange, initialPhotos }) => {
  const [allPhotos, setAllPhotos] = useState({});

  const photoItems = [
    {
      id: 'instalacion_interna',
      titulo: 'Validación de instalación interna – Calidad/Estética',
      showCorregida: true,
    },
    {
      id: 'instalacion_externa',
      titulo: 'Validación de instalación externa – Calidad/Estética',
      showCorregida: true,
    },
    {
      id: 'marquilla',
      titulo: 'Validación marquilla (Roseta abierta y cerrada)',
      showCorregida: true,
    },
    {
      id: 'ubicacion_hgu',
      titulo: 'Ubicación de HGU (funcionalidad / estética)',
      showCorregida: true,
    },
    {
      id: 'bassport',
      titulo: 'Validación de Bassport (ubicación, instalación, configuración)',
      showCorregida: true,
    },
    {
      id: 'test_velocidad',
      titulo: 'Test de velocidad (captura de pantalla del SpeedTest)',
      showCorregida: false,
    },
    {
      id: 'cto',
      titulo: 'CTO',
      showCorregida: true,
    },
    {
      id: 'sistema_tecnico',
      titulo: 'Captura del sistema técnico (Opcional)',
      showCorregida: false,
    },
  ];

  useEffect(() => {
    if (initialPhotos) {
      setAllPhotos(initialPhotos);
    }
  }, [initialPhotos]);

  const handlePhotoChange = (itemId, photos) => {
    const newAllPhotos = {
      ...allPhotos,
      [itemId]: photos,
    };
    
    setAllPhotos(newAllPhotos);
    onPhotosChange(newAllPhotos);
    
    // Guardar automáticamente en storage
    StorageService.savePhotos(newAllPhotos);
  };

  const getPhotoCount = () => {
    let count = 0;
    Object.values(allPhotos).forEach(itemPhotos => {
      if (itemPhotos.hallazgo) count++;
      if (itemPhotos.corregida) count++;
    });
    return count;
  };

  const validatePhotos = () => {
    const requiredItems = photoItems.filter(item => item.id !== 'sistema_tecnico'); // Sistema técnico es opcional
    
    for (let item of requiredItems) {
      const itemPhotos = allPhotos[item.id];
      if (!itemPhotos || !itemPhotos.hallazgo) {
        Alert.alert(
          'Fotos incompletas',
          `Falta la foto con hallazgo para: ${item.titulo}`
        );
        return false;
      }
      
      if (item.showCorregida && !itemPhotos.corregida) {
        Alert.alert(
          'Fotos incompletas',
          `Falta la foto corregida para: ${item.titulo}`
        );
        return false;
      }
    }
    
    return true;
  };

  return (
    <ScrollView style={globalStyles.content} showsVerticalScrollIndicator={false}>
      <View style={globalStyles.card}>
        <Text style={globalStyles.sectionTitle}>Registro Fotográfico</Text>
        <Text style={[globalStyles.label, { textAlign: 'center', marginBottom: 20 }]}>
          Fotos capturadas: {getPhotoCount()}
        </Text>
      </View>

      {photoItems.map((item) => (
        <ItemFotografico
          key={item.id}
          titulo={item.titulo}
          showCorregida={item.showCorregida}
          photos={allPhotos[item.id] || { hallazgo: null, corregida: null }}
          onPhotosChange={(photos) => handlePhotoChange(item.id, photos)}
        />
      ))}

      <View style={{ height: 50 }} />
    </ScrollView>
  );
};

export default RegistroFotografico;
