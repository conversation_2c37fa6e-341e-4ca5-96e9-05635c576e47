@echo off
echo ========================================
echo   Inicializando Gradle
echo ========================================
echo.

cd /d "%~dp0"

echo Verificando estructura de Gradle...
if exist "android\gradle\wrapper\gradle-wrapper.jar" (
    echo ✓ gradle-wrapper.jar encontrado
) else (
    echo ✗ gradle-wrapper.jar no encontrado
    echo Descargando...
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v7.5.1/gradle/wrapper/gradle-wrapper.jar' -OutFile 'android\gradle\wrapper\gradle-wrapper.jar'"
)

if exist "android\gradle\wrapper\gradle-wrapper.properties" (
    echo ✓ gradle-wrapper.properties encontrado
) else (
    echo ✗ gradle-wrapper.properties no encontrado
)

if exist "android\gradlew.bat" (
    echo ✓ gradlew.bat encontrado
) else (
    echo ✗ gradlew.bat no encontrado
)

echo.
echo Limpiando proyecto Android...
cd android

echo Ejecutando Gradle clean...
call gradlew.bat clean

if %errorlevel% neq 0 (
    echo.
    echo Error en Gradle clean. Intentando solucion...
    echo.
    
    REM Eliminar directorios de cache
    if exist ".gradle" rmdir /s /q ".gradle"
    if exist "app\build" rmdir /s /q "app\build"
    if exist "build" rmdir /s /q "build"
    
    echo Cache eliminado. Intentando de nuevo...
    call gradlew.bat clean
)

echo.
echo Construyendo proyecto...
call gradlew.bat assembleDebug

cd ..

echo.
echo ========================================
echo   Inicializacion completada
echo ========================================
echo.
echo Ahora puedes ejecutar:
echo npx react-native run-android
echo.
pause
